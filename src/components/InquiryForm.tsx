import React, { useState } from 'react'
import { X, Calendar, Users, MapPin, Send, Clock, CheckCircle, PartyPopper } from 'lucide-react'

interface InquiryFormProps {
  yacht: {
    id: string
    name: string
    pricePerDay: number
    beginnerFriendly: boolean
    eventFriendly: boolean
    availability: {
      responseTime: string
    }
  }
  onClose: () => void
}

const InquiryForm: React.FC<InquiryFormProps> = ({ yacht, onClose }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    startDate: '',
    endDate: '',
    guests: '2',
    location: '',
    message: '',
    experience: '',
    eventType: '',
    specialRequests: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log('Inquiry submitted:', formData)
    alert(`Thank you for your inquiry! We will contact you ${yacht.availability.responseTime.toLowerCase()}.`)
    onClose()
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const calculateDays = () => {
    if (formData.startDate && formData.endDate) {
      const start = new Date(formData.startDate)
      const end = new Date(formData.endDate)
      const diffTime = Math.abs(end.getTime() - start.getTime())
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays || 1
    }
    return 3
  }

  const days = calculateDays()

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-2xl font-bold text-dark">Send Inquiry</h2>
            <div className="flex items-center space-x-4 mt-1">
              <p className="text-gray-600">{yacht.name}</p>
              <div className="flex items-center space-x-1 text-sm text-accent">
                <Clock className="h-4 w-4" />
                <span>Responds {yacht.availability.responseTime}</span>
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Personal Information */}
          <div>
            <h3 className="text-lg font-semibold text-dark mb-4">Personal Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  value={formData.name}
                  onChange={handleChange}
                  className="input-field"
                  placeholder="Enter your full name"
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="input-field"
                  placeholder="Enter your email"
                />
              </div>
            </div>
            <div className="mt-4">
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                Phone Number
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                className="input-field"
                placeholder="Enter your phone number"
              />
            </div>
          </div>

          {/* Trip Details */}
          <div>
            <h3 className="text-lg font-semibold text-dark mb-4">Trip Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-2">
                  <Calendar className="inline h-4 w-4 mr-1" />
                  Start Date *
                </label>
                <input
                  type="date"
                  id="startDate"
                  name="startDate"
                  required
                  value={formData.startDate}
                  onChange={handleChange}
                  className="input-field"
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-2">
                  <Calendar className="inline h-4 w-4 mr-1" />
                  End Date *
                </label>
                <input
                  type="date"
                  id="endDate"
                  name="endDate"
                  required
                  value={formData.endDate}
                  onChange={handleChange}
                  className="input-field"
                  min={formData.startDate || new Date().toISOString().split('T')[0]}
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <label htmlFor="guests" className="block text-sm font-medium text-gray-700 mb-2">
                  <Users className="inline h-4 w-4 mr-1" />
                  Number of Guests *
                </label>
                <select
                  id="guests"
                  name="guests"
                  required
                  value={formData.guests}
                  onChange={handleChange}
                  className="input-field"
                >
                  {[...Array(12)].map((_, i) => (
                    <option key={i + 1} value={i + 1}>
                      {i + 1} {i === 0 ? 'Guest' : 'Guests'}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                  <MapPin className="inline h-4 w-4 mr-1" />
                  Preferred Location
                </label>
                <input
                  type="text"
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleChange}
                  className="input-field"
                  placeholder="e.g., Amalfi Coast, French Riviera"
                />
              </div>
            </div>
          </div>

          {/* Experience Level - For beginners */}
          {yacht.beginnerFriendly && (
            <div className="bg-accent bg-opacity-10 rounded-lg p-4">
              <h4 className="font-semibold text-dark mb-3 flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-accent" />
                <span>Experience Level</span>
              </h4>
              <select
                id="experience"
                name="experience"
                value={formData.experience}
                onChange={handleChange}
                className="input-field"
              >
                <option value="">Select your experience level</option>
                <option value="first-time">First time on a yacht</option>
                <option value="beginner">Some boating experience</option>
                <option value="experienced">Experienced sailor</option>
              </select>
              <p className="text-sm text-gray-600 mt-2">
                This helps us provide the best experience and safety briefing for your group.
              </p>
            </div>
          )}

          {/* Event Details - For event organizers */}
          {yacht.eventFriendly && (
            <div className="bg-purple-50 rounded-lg p-4">
              <h4 className="font-semibold text-dark mb-3 flex items-center space-x-2">
                <PartyPopper className="h-5 w-5 text-purple-600" />
                <span>Event Details</span>
              </h4>
              <div className="space-y-4">
                <div>
                  <label htmlFor="eventType" className="block text-sm font-medium text-gray-700 mb-2">
                    Type of Event
                  </label>
                  <select
                    id="eventType"
                    name="eventType"
                    value={formData.eventType}
                    onChange={handleChange}
                    className="input-field"
                  >
                    <option value="">Select event type</option>
                    <option value="birthday">Birthday Party</option>
                    <option value="wedding">Wedding Celebration</option>
                    <option value="corporate">Corporate Event</option>
                    <option value="anniversary">Anniversary</option>
                    <option value="other">Other Celebration</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="specialRequests" className="block text-sm font-medium text-gray-700 mb-2">
                    Special Requirements
                  </label>
                  <textarea
                    id="specialRequests"
                    name="specialRequests"
                    rows={3}
                    value={formData.specialRequests}
                    onChange={handleChange}
                    className="input-field resize-none"
                    placeholder="Catering needs, decorations, music, photography, etc."
                  />
                </div>
              </div>
            </div>
          )}

          {/* Message */}
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
              Additional Message
            </label>
            <textarea
              id="message"
              name="message"
              rows={4}
              value={formData.message}
              onChange={handleChange}
              className="input-field resize-none"
              placeholder="Tell us about your plans, any questions, or special requests..."
            />
          </div>

          {/* Pricing Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-semibold text-dark mb-3">Estimated Pricing</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between text-gray-600">
                <span>Base price ({days} {days === 1 ? 'day' : 'days'})</span>
                <span>€{yacht.pricePerDay * days}</span>
              </div>
              <div className="flex justify-between text-gray-600">
                <span>Service & cleaning fees</span>
                <span>€250</span>
              </div>
              <div className="border-t pt-2 mt-2">
                <div className="flex justify-between font-semibold">
                  <span>Estimated Total</span>
                  <span>€{yacht.pricePerDay * days + 250}</span>
                </div>
              </div>
            </div>
            <div className="text-xs text-gray-500 mt-2">
              * Final pricing will be confirmed based on your specific requirements and dates
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex space-x-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 btn-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 btn-primary flex items-center justify-center space-x-2"
            >
              <Send className="h-5 w-5" />
              <span>Send Inquiry</span>
            </button>
          </div>

          {/* Response Time Reminder */}
          <div className="text-center p-3 bg-accent bg-opacity-10 rounded-lg">
            <div className="flex items-center justify-center space-x-2 text-accent">
              <Clock className="h-4 w-4" />
              <span className="text-sm font-medium">
                You'll receive a response {yacht.availability.responseTime.toLowerCase()}
              </span>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}

export default InquiryForm