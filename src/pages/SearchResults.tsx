import { useState, useMemo } from 'react'
import { Link } from 'react-router-dom'
import { Users, Calendar, Grid, List, Star, Clock, CheckCircle, PartyPopper, ShipWheel as Wheel, Ruler } from 'lucide-react'
import { yachts } from '../data/yachts'

const SearchResults = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState('recommended')
  const [filters, setFilters] = useState({
    priceRange: 'all',
    capacity: 'all',
    experience: 'all',
    luxury: 'all',
    events: false
  })

  const filteredYachts = useMemo(() => {
    let filtered = [...yachts]

    // Apply filters
    if (filters.priceRange !== 'all') {
      const [min, max] = filters.priceRange.split('-').map(Number)
      filtered = filtered.filter(yacht => yacht.pricePerDay >= min && yacht.pricePerDay <= max)
    }

    if (filters.capacity !== 'all') {
      const capacity = parseInt(filters.capacity)
      filtered = filtered.filter(yacht => yacht.capacity >= capacity)
    }

    if (filters.experience === 'beginner') {
      filtered = filtered.filter(yacht => yacht.beginnerFriendly)
    }

    if (filters.luxury !== 'all') {
      filtered = filtered.filter(yacht => yacht.luxuryLevel === filters.luxury)
    }

    if (filters.events) {
      filtered = filtered.filter(yacht => yacht.eventFriendly)
    }

    // Apply sorting
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.pricePerDay - b.pricePerDay)
        break
      case 'price-high':
        filtered.sort((a, b) => b.pricePerDay - a.pricePerDay)
        break
      case 'rating':
        filtered.sort((a, b) => b.reviews.rating - a.reviews.rating)
        break
      case 'capacity':
        filtered.sort((a, b) => b.capacity - a.capacity)
        break
      default:
        // Keep recommended order
        break
    }

    return filtered
  }, [filters, sortBy])

  const clearAllFilters = () => {
    setFilters({
      priceRange: 'all',
      capacity: 'all',
      experience: 'all',
      luxury: 'all',
      events: false
    })
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric'
    })
  }

  const hasActiveFilters = () => {
    return filters.priceRange !== 'all' || 
           filters.capacity !== 'all' || 
           filters.experience !== 'all' || 
           filters.luxury !== 'all' || 
           filters.events
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Search Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-dark mb-2">Premium Yachts Available</h1>
        <p className="text-gray-600">Discover luxury yachts for your perfect maritime experience</p>
      </div>

      {/* Quick Filters for Personas */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => setFilters({...filters, luxury: 'ultra-luxury', events: false, experience: 'all'})}
            className="inline-flex items-center space-x-2 px-4 py-2 bg-yellow-100 text-yellow-800 rounded-full hover:bg-yellow-200 transition-colors font-medium min-w-0"
          >
            <span>👑</span>
            <span className="whitespace-nowrap">Ultra Luxury</span>
          </button>
          <button
            onClick={() => setFilters({...filters, experience: 'beginner', events: false, luxury: 'all'})}
            className="inline-flex items-center space-x-2 px-4 py-2 bg-accent bg-opacity-20 text-accent rounded-full hover:bg-accent hover:bg-opacity-30 transition-colors font-medium min-w-0"
          >
            <CheckCircle className="h-4 w-4 flex-shrink-0" />
            <span className="whitespace-nowrap">Beginner Friendly</span>
          </button>
          <button
            onClick={() => setFilters({...filters, events: true, experience: 'all', luxury: 'all'})}
            className="inline-flex items-center space-x-2 px-4 py-2 bg-purple-100 text-purple-600 rounded-full hover:bg-purple-200 transition-colors font-medium min-w-0"
          >
            <PartyPopper className="h-4 w-4 flex-shrink-0" />
            <span className="whitespace-nowrap">Events</span>
          </button>
          {hasActiveFilters() && (
            <button
              onClick={clearAllFilters}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors font-medium min-w-0"
            >
              <span className="whitespace-nowrap">Show All</span>
            </button>
          )}
        </div>
      </div>

      {/* Advanced Filters and Controls */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 space-y-4 lg:space-y-0">
        <div className="flex flex-wrap items-center gap-4">
          <select 
            value={filters.priceRange}
            onChange={(e) => setFilters({...filters, priceRange: e.target.value})}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="all">All Prices</option>
            <option value="0-1500">Under €1,500</option>
            <option value="1500-2500">€1,500 - €2,500</option>
            <option value="2500-5000">€2,500+</option>
          </select>

          <select 
            value={filters.capacity}
            onChange={(e) => setFilters({...filters, capacity: e.target.value})}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="all">Any Size</option>
            <option value="6">6+ Guests</option>
            <option value="10">10+ Guests</option>
            <option value="12">12+ Guests</option>
          </select>

          <select 
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="recommended">Recommended</option>
            <option value="price-low">Price: Low to High</option>
            <option value="price-high">Price: High to Low</option>
            <option value="rating">Highest Rated</option>
            <option value="capacity">Largest First</option>
          </select>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'grid' ? 'bg-primary-500 text-white' : 'text-gray-500 hover:text-primary-500'
            }`}
          >
            <Grid className="h-5 w-5" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'list' ? 'bg-primary-500 text-white' : 'text-gray-500 hover:text-primary-500'
            }`}
          >
            <List className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Results Count */}
      <div className="mb-6">
        <p className="text-gray-600">{filteredYachts.length} yachts available</p>
      </div>

      {/* Yacht Grid */}
      <div className={`grid gap-6 ${
        viewMode === 'grid' 
          ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
          : 'grid-cols-1'
      }`}>
        {filteredYachts.map((yacht) => (
          <Link
            key={yacht.id}
            to={`/boat/${yacht.id}`}
            className={`card overflow-hidden group ${
              viewMode === 'list' ? 'flex h-80' : 'block'
            }`}
          >
            <div className={`relative ${viewMode === 'list' ? 'w-80 flex-shrink-0' : 'w-full h-64'}`}>
              <img
                src={yacht.images[0]}
                alt={yacht.name}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute top-4 left-4 flex flex-col space-y-2">
                {yacht.luxuryLevel === 'ultra-luxury' && (
                  <span className="inline-flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 min-w-0">
                    <span>👑</span>
                    <span className="whitespace-nowrap">Ultra Luxury</span>
                  </span>
                )}
                {yacht.luxuryLevel === 'premium' && (
                  <span className="inline-flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 min-w-0">
                    <span>⭐</span>
                    <span className="whitespace-nowrap">Premium</span>
                  </span>
                )}
                {yacht.beginnerFriendly && (
                  <span className="inline-flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium bg-accent text-white min-w-0">
                    <CheckCircle className="h-4 w-4 flex-shrink-0" />
                    <span className="whitespace-nowrap">Beginner Friendly</span>
                  </span>
                )}
              </div>
              <div className="absolute top-4 right-4">
                <span className="bg-white bg-opacity-95 px-3 py-2 rounded-lg text-sm font-bold text-dark shadow-sm">
                  €{yacht.pricePerDay}/day
                </span>
              </div>
              <div className="absolute bottom-4 right-4">
                <div className="flex items-center space-x-1 bg-white bg-opacity-95 px-2 py-1 rounded-lg">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm font-medium">{yacht.reviews.rating}</span>
                  <span className="text-xs text-gray-600">({yacht.reviews.count})</span>
                </div>
              </div>
            </div>

            <div className={`p-6 ${viewMode === 'list' ? 'flex-1 flex flex-col' : ''}`}>
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="text-xl font-semibold text-dark group-hover:text-primary-500 transition-colors">
                    {yacht.name}
                  </h3>
                  <p className="text-gray-600 text-sm">{yacht.manufacturer} • {yacht.year}</p>
                </div>
                <div className="flex items-center space-x-1 text-sm text-gray-600">
                  <Clock className="h-4 w-4" />
                  <span>{yacht.availability.responseTime}</span>
                </div>
              </div>

              {/* Key Info Grid */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Users className="h-4 w-4" />
                  <span>{yacht.capacity} guests</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Ruler className="h-4 w-4" />
                  <span>{yacht.length}ft</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Wheel className="h-4 w-4" />
                  <span>{yacht.skipper.included ? 'Skipper Included' : 'Self-Drive'}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Calendar className="h-4 w-4" />
                  <span>Available {formatDate(yacht.availability.nextAvailable)}</span>
                </div>
              </div>

              {/* Description - moved below grid and made less prominent */}
              <p className="text-gray-500 text-sm mb-4 line-clamp-2">{yacht.description}</p>

              {/* What's Included */}
              <div className={viewMode === 'list' ? 'flex-1' : ''}>
                <p className="text-xs font-medium text-gray-500 mb-2">INCLUDED:</p>
                <div className="flex flex-wrap gap-1">
                  {yacht.included.slice(0, 3).map((item, index) => (
                    <span key={index} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                      {item}
                    </span>
                  ))}
                  {yacht.included.length > 3 && (
                    <span className="text-xs text-gray-500">+{yacht.included.length - 3} more</span>
                  )}
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* No Results */}
      {filteredYachts.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🛥️</div>
          <h3 className="text-xl font-semibold text-dark mb-2">No yachts match your criteria</h3>
          <p className="text-gray-600 mb-4">Try adjusting your filters to see more options</p>
          <button
            onClick={clearAllFilters}
            className="btn-primary"
          >
            Clear All Filters
          </button>
        </div>
      )}
    </div>
  )
}

export default SearchResults