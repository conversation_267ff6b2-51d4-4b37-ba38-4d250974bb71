import React, { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { ArrowLeft, Users, MapPin, Calendar, Fuel, Star, Wifi, Car, Utensils, Waves, Send, Clock, CheckCircle, Shield, Award, PartyPopper, ShipWheel as Wheel, Ruler } from 'lucide-react'
import { yachts } from '../data/yachts'
import InquiryForm from '../components/InquiryForm'

const BoatDetail = () => {
  const { id } = useParams()
  const yacht = yachts.find(y => y.id === id)
  const [selectedImage, setSelectedImage] = useState(0)
  const [showInquiryForm, setShowInquiryForm] = useState(false)

  if (!yacht) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-dark mb-4">Yacht not found</h1>
          <Link to="/" className="btn-primary">
            Back to Search
          </Link>
        </div>
      </div>
    )
  }

  const featureIcons = {
    'WiFi': Wifi,
    'Parking': Car,
    'Kitchen': Utensils,
    'Gourmet Kitchen': Utensils,
    'Water Sports Equipment': Waves,
    'Water Sports': Waves,
  }

  const getLuxuryBadge = () => {
    switch (yacht.luxuryLevel) {
      case 'ultra-luxury':
        return { icon: '👑', text: 'Ultra Luxury', color: 'bg-yellow-100 text-yellow-800' }
      case 'premium':
        return { icon: '⭐', text: 'Premium', color: 'bg-blue-100 text-blue-800' }
      default:
        return { icon: '🚤', text: 'Standard', color: 'bg-gray-100 text-gray-800' }
    }
  }

  const luxuryBadge = getLuxuryBadge()

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Back Button */}
      <Link
        to="/"
        className="inline-flex items-center space-x-2 text-primary-500 hover:text-primary-600 mb-6 transition-colors"
      >
        <ArrowLeft className="h-5 w-5" />
        <span>Back to Search</span>
      </Link>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          {/* Image Gallery */}
          <div className="mb-8">
            <div className="relative mb-4">
              <img
                src={yacht.images[selectedImage]}
                alt={yacht.name}
                className="w-full h-96 object-cover rounded-xl"
              />
              <div className="absolute top-4 left-4 flex flex-col space-y-2">
                <span className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${luxuryBadge.color}`}>
                  <span>{luxuryBadge.icon}</span>
                  <span>{luxuryBadge.text}</span>
                </span>
                {yacht.beginnerFriendly && (
                  <span className="bg-accent bg-opacity-90 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                    <CheckCircle className="h-4 w-4" />
                    <span>Great for Beginners</span>
                  </span>
                )}
                {yacht.eventFriendly && (
                  <span className="bg-purple-500 bg-opacity-90 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                    <PartyPopper className="h-4 w-4" />
                    <span>Event Ready</span>
                  </span>
                )}
              </div>
              <div className="absolute bottom-4 right-4">
                <div className="flex items-center space-x-2 bg-white bg-opacity-95 px-3 py-2 rounded-lg shadow-sm">
                  <Clock className="h-4 w-4 text-accent" />
                  <span className="text-sm font-medium">Responds {yacht.availability.responseTime}</span>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-4 gap-2">
              {yacht.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`relative h-20 rounded-lg overflow-hidden ${
                    selectedImage === index ? 'ring-2 ring-primary-500' : ''
                  }`}
                >
                  <img
                    src={image}
                    alt={`${yacht.name} ${index + 1}`}
                    className="w-full h-full object-cover hover:scale-105 transition-transform"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Yacht Info */}
          <div className="mb-8">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h1 className="text-3xl font-bold text-dark mb-2">{yacht.name}</h1>
                <p className="text-gray-600">{yacht.manufacturer} • {yacht.year}</p>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-primary-500">€{yacht.pricePerDay}</div>
                <div className="text-gray-600">per day</div>
              </div>
            </div>

            <div className="flex items-center space-x-6 mb-6">
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className={`h-5 w-5 ${i < Math.floor(yacht.reviews.rating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} />
                ))}
                <span className="text-gray-600 ml-2">{yacht.reviews.rating} ({yacht.reviews.count} reviews)</span>
              </div>
              <div className="flex items-center space-x-1 text-sm text-accent">
                <Shield className="h-4 w-4" />
                <span>Verified Owner</span>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                <Users className="h-5 w-5 text-primary-500" />
                <div>
                  <div className="font-medium">{yacht.capacity}</div>
                  <div className="text-sm text-gray-600">Guests</div>
                </div>
              </div>
              <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                <Ruler className="h-5 w-5 text-primary-500" />
                <div>
                  <div className="font-medium">{yacht.length}ft</div>
                  <div className="text-sm text-gray-600">Length</div>
                </div>
              </div>
              <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                <Fuel className="h-5 w-5 text-primary-500" />
                <div>
                  <div className="font-medium">{yacht.engine}</div>
                  <div className="text-sm text-gray-600">Engine</div>
                </div>
              </div>
              <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                <Calendar className="h-5 w-5 text-primary-500" />
                <div>
                  <div className="font-medium">Available</div>
                  <div className="text-sm text-gray-600">{yacht.availability.nextAvailable}</div>
                </div>
              </div>
            </div>
          </div>

          {/* What's Included - Prominent for beginners */}
          <div className="mb-8 bg-accent bg-opacity-10 rounded-xl p-6">
            <h2 className="text-xl font-bold text-dark mb-4 flex items-center space-x-2">
              <CheckCircle className="h-6 w-6 text-accent" />
              <span>What's Included</span>
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {yacht.included.map((item, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="h-4 w-4 text-accent flex-shrink-0" />
                  <span className="text-gray-700">{item}</span>
                </div>
              ))}
            </div>
            {yacht.skipper.required && !yacht.skipper.included && (
              <div className="mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                <p className="text-sm text-yellow-800">
                  <strong>Skipper Required:</strong> Professional skipper available for €{yacht.skipper.cost}/day
                </p>
              </div>
            )}
          </div>

          {/* Description */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-dark mb-4">About this yacht</h2>
            <p className="text-gray-700 leading-relaxed text-lg">{yacht.description}</p>
          </div>

          {/* Features */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-dark mb-4">Features & Amenities</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {yacht.features.map((feature, index) => {
                const IconComponent = featureIcons[feature as keyof typeof featureIcons] || Waves
                return (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <IconComponent className="h-5 w-5 text-primary-500" />
                    <span className="font-medium">{feature}</span>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Event Amenities - For event organizers */}
          {yacht.eventFriendly && yacht.eventAmenities && (
            <div className="mb-8 bg-purple-50 rounded-xl p-6">
              <h2 className="text-2xl font-bold text-dark mb-4 flex items-center space-x-2">
                <PartyPopper className="h-6 w-6 text-purple-600" />
                <span>Perfect for Events</span>
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {yacht.eventAmenities.map((amenity, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <PartyPopper className="h-4 w-4 text-purple-600 flex-shrink-0" />
                    <span className="text-gray-700">{amenity}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Reviews Highlights */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-dark mb-4 flex items-center space-x-2">
              <Award className="h-6 w-6 text-yellow-500" />
              <span>Guest Highlights</span>
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {yacht.reviews.highlights.map((highlight, index) => (
                <div key={index} className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium text-yellow-800">Guest Favorite</span>
                  </div>
                  <p className="text-gray-700">"{highlight}"</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="card p-6 sticky top-24">
            <div className="text-center mb-6">
              <div className="text-3xl font-bold text-primary-500 mb-1">€{yacht.pricePerDay}</div>
              <div className="text-gray-600">per day</div>
              <div className="flex items-center justify-center space-x-1 mt-2">
                <Clock className="h-4 w-4 text-accent" />
                <span className="text-sm text-accent font-medium">Responds {yacht.availability.responseTime}</span>
              </div>
            </div>

            <div className="space-y-4 mb-6">
              <button
                onClick={() => setShowInquiryForm(true)}
                className="btn-primary w-full flex items-center justify-center space-x-2"
              >
                <Send className="h-5 w-5" />
                <span>Send Inquiry</span>
              </button>
              <button className="btn-secondary w-full">
                Check Availability
              </button>
            </div>

            {/* Pricing Breakdown - Clear for luxury seekers */}
            <div className="border-t pt-4">
              <h4 className="font-semibold text-dark mb-3">Pricing Breakdown</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between text-gray-600">
                  <span>Base price (3 days)</span>
                  <span>€{yacht.pricePerDay * 3}</span>
                </div>
                {!yacht.skipper.included && yacht.skipper.required && (
                  <div className="flex items-center justify-between text-gray-600">
                    <span>Skipper (3 days)</span>
                    <span>€{yacht.skipper.cost! * 3}</span>
                  </div>
                )}
                <div className="flex items-center justify-between text-gray-600">
                  <span>Service fee</span>
                  <span>€150</span>
                </div>
                <div className="flex items-center justify-between text-gray-600">
                  <span>Cleaning fee</span>
                  <span>€100</span>
                </div>
                <div className="border-t pt-2 mt-3">
                  <div className="flex items-center justify-between font-bold text-lg">
                    <span>Total</span>
                    <span>€{yacht.pricePerDay * 3 + (yacht.skipper.required && !yacht.skipper.included ? yacht.skipper.cost! * 3 : 0) + 250}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="border-t pt-4 mt-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Shield className="h-4 w-4 text-accent" />
                  <span>Verified owner & yacht</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <CheckCircle className="h-4 w-4 text-accent" />
                  <span>Instant booking available</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Award className="h-4 w-4 text-yellow-500" />
                  <span>{yacht.reviews.rating}★ ({yacht.reviews.count} reviews)</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Inquiry Form Modal */}
      {showInquiryForm && (
        <InquiryForm
          yacht={yacht}
          onClose={() => setShowInquiryForm(false)}
        />
      )}
    </div>
  )
}

export default BoatDetail