/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f4ff',
          100: '#e0e9ff',
          500: '#1C40A5',
          600: '#1a3a94',
          700: '#183384',
          800: '#162d73',
          900: '#1E2344',
        },
        accent: '#67D6A6',
        dark: '#1E2344',
      },
      fontFamily: {
        'archivo': ['Archivo', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
    },
  },
  plugins: [],
}